<template>
  <el-form ref="serverSetting" :model="serverSetting" label-position="left" :label-width="labelWidth" :rules="serverSettingRules">
    <el-form-item prop="serverAddr">
      <template #label>
        <EllipsisText :content="$t('dialog.serverAddress') + ':'" class="form-label" />
      </template>
      <bfInput v-model="serverSetting.serverAddr" :maxlength="95" />
    </el-form-item>
    <el-form-item prop="serverPort">
      <template #label>
        <EllipsisText :content="$t('dialog.serverPort') + ':'" class="form-label" />
      </template>
      <bfInput v-model.number="serverSetting.serverPort" />
    </el-form-item>
    <el-form-item prop="localPort">
      <template #label>
        <EllipsisText :content="$t('dialog.localPort') + ':'" class="form-label" />
      </template>
      <bfInput v-model.number="serverSetting.localPort" />
    </el-form-item>
    <el-form-item label=" " class="center actions">
      <bf-button color-type="primary" :disabled="disabled" @click="queryServerSetting" v-text="$t('dialog.querySetting')" />
      <bf-button color-type="warning" :disabled="disabled" @click="updateServerSetting" v-text="$t('dialog.writeIn')" />
    </el-form-item>
  </el-form>
</template>

<script>
  import repeaterWfMod, { DefModel, TR925Models } from '@/writingFrequency/repeater'
  import bfutil from '@/utils/bfutil'
  import { kcpPackageName } from '@/modules/protocol'
  import { merge } from 'lodash'
  import bfInput from '@/components/bfInput/main'
  import bfButton from '@/components/bfButton/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import { convertPxToRem, calcScaleSize } from '@/utils/setRem'

  const ServerSetting = {
    serverAddr: '',
    serverPort: '',
    localPort: '',
  }

  export default {
    name: 'ServerSetting',
    props: {
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      packageName: {
        type: String,
        default: '',
      },
      deviceModel: {
        type: String,
        default: DefModel,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
    },
    components: {
      bfInput,
      bfButton,
      EllipsisText,
    },
    data() {
      return {
        serverSetting: {
          ...ServerSetting,
        },
      }
    },
    methods: {
      saveConfig(data) {
        this.serverSetting = merge(this.serverSetting, data)

        this.saveMethod('serverSetting', data)
      },
      // server setting
      queryServerSetting() {
        const options = merge(this.defQueryOption, {
          paraBin: {
            tableId: 5,
          },
        })

        repeaterWfMod
          .queryConfig(options)
          .then(res => {
            return this.saveConfig(res)
          })
          .catch(err => {
            bfglob.console.error('queryServerSetting', err)
          })
      },
      updateServerSetting() {
        this.$refs.serverSetting.validate(valid => {
          if (!valid) {
            return false
          }

          const options = merge(this.defQueryOption, {
            paraBin: {
              operation: 6,
              tableId: 5,
            },
          })

          repeaterWfMod
            .writeInData(this.serverSetting, options)
            .then(res => {
              return this.saveConfig(this.serverSetting)
            })
            .catch(err => {
              bfglob.console.error('updateServerSetting', err)
            })
        })
      },
    },
    computed: {
      labelWidth() {
        return convertPxToRem(calcScaleSize(150)) + 'rem'
      },
      defQueryOption() {
        return {
          paraBin: {
            operation: 5,
          },
          sid: this.repeater,
          paraInt: this.getRepeaterId(),
          decodeMsgType: 'RepeaterServerSetting',
          packageName: this.packageName || kcpPackageName,
        }
      },
      isTR925Model() {
        return TR925Models.includes(this.deviceModel.slice(0, 6))
      },
      serverSettingRules() {
        return {
          serverAddr: [
            {
              validator: (rule, value, callback) => {
                bfutil.mustIpRuleOrDormain(rule, value, callback)
              },
              trigger: ['blur'],
            },
          ],
          serverPort: [
            {
              validator: (rule, value, callback) => {
                if (!value) {
                  callback(new Error(this.$t('dialog.requiredRule')))
                } else {
                  bfutil.mustIpPort(rule, value, callback)
                }
              },
              trigger: ['blur'],
            },
          ],
          localPort: [
            {
              validator: (rule, value, callback) => {
                if (!value) {
                  callback(new Error(this.$t('dialog.requiredRule')))
                } else {
                  bfutil.mustIpPort(rule, value, callback)
                }
              },
              trigger: ['blur'],
            },
          ],
        }
      },
    },
    watch: {
      'repeaterData.writeFrequencySetting.serverSetting': {
        immediate: true,
        deep: true,
        handler(val) {
          this.serverSetting = merge({}, val || ServerSetting)
        },
      },
    },
  }
</script>

<style lang="scss">
  .form-label {
    height: 50px;
    line-height: 50px;
  }
</style>
