<template>
  <el-form ref="channelSetting" class="tr805005-channel" :model="channelSetting" label-position="left" :label-width="labelWidth" :rules="channelSettingRules">
    <el-form-item prop="chId">
      <template #label>
        <EllipsisText :content="$t('dialog.chId') + ':'" class="form-label" />
      </template>
      <bfInputNumberV2 v-model="channelSetting.chId" :min="1" :max="99" :step="1" :disabled="queryCurChInfo" class="!w-full" />
    </el-form-item>
    <el-form-item prop="chName">
      <template #label>
        <EllipsisText :content="$t('dialog.chName') + ':'" class="form-label" />
      </template>
      <bfInput v-model="channelSetting.chName" :maxlength="8" :readonly="!modify" />
    </el-form-item>
    <el-form-item prop="chType">
      <template #label>
        <EllipsisText :content="$t('dialog.chType') + ':'" class="form-label" />
      </template>
      <bfSelect
        v-model="channelSetting.chType"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        :readonly="!modify"
        class="!w-full"
      >
        <el-option v-for="(item, i) in channelTypes" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item prop="txPower">
      <template #label>
        <EllipsisText :content="$t('dialog.txPower') + ':'" class="form-label" />
      </template>
      <bfSelect
        v-model="channelSetting.txPower"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        :readonly="!modify"
        class="!w-full"
      >
        <el-option v-for="(item, i) in txPowerTypes" :key="i" :label="item.label" :value="item.value" />
      </bfSelect>
    </el-form-item>
    <el-form-item prop="rxFrequency">
      <template #label>
        <EllipsisText :content="$t('dialog.rxFrequency') + ':'" class="form-label" />
      </template>
      <FrequencyMhz v-model="channelSetting.rxFrequency" :maxlength="9" :readonly="!modify" />
    </el-form-item>
    <el-form-item prop="txFrequency">
      <template #label>
        <EllipsisText :content="$t('dialog.txFrequency') + ':'" class="form-label" />
      </template>
      <FrequencyMhz v-model="channelSetting.txFrequency" :maxlength="9" :readonly="!modify" />
    </el-form-item>
    <el-form-item prop="colourCodes">
      <template #label>
        <EllipsisText :content="$t('dialog.colorCodes') + ':'" class="form-label" />
      </template>
      <bfInputNumberV2 v-model="channelSetting.colourCodes" :min="0" :max="15" :step="1" :disabled="!modify" class="!w-full" />
    </el-form-item>
    <el-form-item v-if="queryCurChInfo" label=" " class="center actions">
      <bfButton color-type="primary" :disabled="disabled" @click="queryRepeaterCurChInfo" v-text="$t('dialog.querySetting')" />
      <bfButton v-if="modify" color-type="primary" :disabled="disabled" @click="writeInCurChInfo" v-text="$t('dialog.writeIn')" />
    </el-form-item>
    <el-form-item v-else label-width="0" class="center actions">
      <bfButton color-type="primary" :disabled="disabled" @click="queryChannelSettings(0)" v-text="$t('dialog.queryAllChannel')" />
      <bfButton color-type="primary" :disabled="disabled" @click="queryChannelSetting" v-text="$t('dialog.querySetting')" />
      <bfButton color-type="warning" :disabled="disabled" @click="writeInChannelSetting" v-text="$t('dialog.writeIn')" />
      <bfButton color-type="danger" :disabled="disabled" @click="deleteChannelSetting" v-text="$t('dialog.delete')" />
    </el-form-item>
  </el-form>
</template>

<script>
  import repeaterWfMod, { DefModel } from '@/writingFrequency/repeater'
  import bfutil from '@/utils/bfutil'
  import bfproto, { kcpPackageName } from '@/modules/protocol'
  import { cloneDeep, merge } from 'lodash'
  import { readStringU16, writeStringU16 } from '@/writingFrequency/interphone/common'
  import { defineAsyncComponent } from 'vue'
  import bfInput from '@/components/bfInput/main.ts'
  import bfInputNumberV2 from '@/components/bfInputNumber/main.ts'
  import bfSelect from '@/components/bfSelect/main.ts'
  import bfButton from '@/components/bfButton/main.ts'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import { convertPxToRem, calcScaleSize } from '@/utils/setRem'

  const ChannelSetting = {
    chId: 1,
    chName: '',
    chType: 0,
    colourCodes: 1,
    rxFrequency: 0,
    txFrequency: 0,
    txPower: 0,
  }
  const RepeaterCurChInfo = {
    zoneId: {
      // 一级区域ID
      mainZoneId: 0,
      // 二级区域ID
      subZoneId: 0,
      // 三级区域ID
      userZoneId: 0,
    },
    channelInfo: {
      ...ChannelSetting,
    },
    curstomPowerLv: 0,
  }
  export default {
    name: 'TR805005Channel',
    props: {
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      packageName: {
        type: String,
        default: kcpPackageName,
      },
      deviceModel: {
        type: String,
        default: DefModel,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
      queryCurChInfo: {
        type: Boolean,
        default: false,
      },
      modify: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        repeaterReplyPrefix: 'repeater',
        channelSetting: cloneDeep(ChannelSetting),
        repeaterCurChInfo: cloneDeep(RepeaterCurChInfo),
      }
    },
    methods: {
      getAllChannelSetting() {
        if (!this.repeaterData) {
          return undefined
        }
        const writeFrequencySetting = this.repeaterData.writeFrequencySetting
        if (!writeFrequencySetting) {
          return undefined
        }
        const allChannelSetting = writeFrequencySetting.allChannelSetting
        if (!allChannelSetting) {
          return undefined
        }

        return cloneDeep(allChannelSetting)
      },
      setChannelSetting(data) {
        this.channelSetting = Object.assign({}, this.channelSetting, data)
      },
      getChannelSetting() {
        return Object.assign({}, this.channelSetting)
      },

      // 保存读取的信道数据
      saveChannelData(settings, isEnd) {
        // 同步本地中继所有信道数据
        let allChannelSetting = this.getAllChannelSetting()
        if (!allChannelSetting) {
          allChannelSetting = {}
        }
        allChannelSetting[settings.chId] = bfproto.copyFieldsFromProto(settings)
        this.saveMethod('allChannelSetting', allChannelSetting)

        if (this.channelSetting.chId === settings.chId) {
          this.setChannelSetting(settings)
        }

        // 全部查询完成，判断当前显示的信道是否拥有，没有则显示默认的第一个信道
        if (!isEnd) {
          return
        }

        if (allChannelSetting) {
          const chIds = Object.keys(allChannelSetting)
          if (!chIds.includes(this.channelSetting.chId + '')) {
            this.setChannelSetting(allChannelSetting[chIds.shift()])
          }
        }
      },
      // 信道设置
      queryChannelSettings(chNo) {
        const options = merge(this.defQueryOption, {
          paraBin: {
            operation: 5,
            tableId: 6,
            objIndex: chNo,
          },
          stream: !chNo,
        })

        // 全部查询信道，需要订阅相关消息事件
        if (options.stream) {
          bfglob.on(this.repeaterReplyPrefix + options.paraInt, (rpc_cmd_obj, isEnd) => {
            this.saveChannelData(rpc_cmd_obj.body, isEnd)
          })

          bfglob.once('failed:' + this.repeaterReplyPrefix + options.paraInt, rpc_cmd_obj => {
            bfglob.console.warn('queryChannelSettings failed', rpc_cmd_obj)
          })
        }

        repeaterWfMod
          .queryConfig(options)
          .then(res => {
            this.saveChannelData(res, true)
          })
          .catch(err => {
            bfglob.console.error('queryChannelSettings', err)
          })
      },
      queryChannelSetting() {
        this.queryChannelSettings(this.channelSetting.chId)
      },
      writeInChannelSetting() {
        this.$refs.channelSetting.validate(valid => {
          if (!valid) {
            return false
          }

          const channelData = this.getChannelSetting()
          const options = merge(this.defQueryOption, {
            paraBin: {
              operation: 6,
              tableId: 6,
              objIndex: channelData.chId,
            },
          })

          repeaterWfMod
            .writeInData(channelData, options)
            .then(res => {
              return this.saveChannelData(channelData, true)
            })
            .catch(err => {
              bfglob.console.error('writeInCurChInfo', err)
            })
        })
      },
      deleteChannelConfigByChId(chId) {
        const allChannelSetting = this.getAllChannelSetting()
        if (!allChannelSetting) {
          return
        }

        // eslint-disable-next-line
        delete this.repeaterData.writeFrequencySetting.allChannelSetting[chId]
        // eslint-disable-next-line
        this.repeaterData.writeFrequencySetting.allChannelSetting = {
          ...this.repeaterData.writeFrequencySetting.allChannelSetting,
        }
      },
      deleteChannelSetting() {
        this.$refs.channelSetting.validate(valid => {
          if (!valid) {
            return false
          }

          const channelData = this.getChannelSetting()
          const options = merge(this.defQueryOption, {
            paraBin: {
              operation: 7,
              tableId: 6,
              objIndex: channelData.chId,
            },
          })

          repeaterWfMod
            .writeInData(channelData, options)
            .then(res => {
              // 删除本地对应的信道数据
              this.deleteChannelConfigByChId(channelData.chId)

              const allChannelSetting = this.getAllChannelSetting()
              const chIds = Object.keys(allChannelSetting)
              this.setChannelSetting(allChannelSetting[chIds.shift()])
            })
            .catch(err => {
              bfglob.console.error('deleteChannelSetting', err)
            })
        })
      },

      decodeChannelName(bytesName) {
        switch (this.deviceModel) {
          case 'TR900M':
          case 'TR925M':
            if (Array.isArray(bytesName)) {
              bytesName = new Uint8Array(bytesName)
            }
            return readStringU16(bytesName, 0, bytesName.length)
          default:
            return bytesName
        }
      },
      encodeChannelName(strName) {
        switch (this.deviceModel) {
          case 'TR900M':
          case 'TR925M':
            return writeStringU16(strName)
          default:
            return strName
        }
      },
      // 10:查询中继当前信道信息
      queryRepeaterCurChInfo() {
        const options = merge(this.defQueryOption, {
          paraBin: {
            operation: 10,
          },
          decodeMsgType: 'RepeaterCurChInfo',
        })

        repeaterWfMod
          .queryConfig(options)
          .then(res => {
            // 解码bytes类型的信道名称
            res.channelInfo.chName = this.decodeChannelName(res.channelInfo.chName)
            this.repeaterCurChInfo = merge(this.repeaterCurChInfo, res)
            this.saveMethod('repeaterCurChInfo', this.repeaterCurChInfo)
          })
          .catch(err => {
            bfglob.console.error('queryRepeaterCurChInfo', err)
          })
      },

      // 13:修改当前信道信道
      writeInCurChInfo() {
        this.$refs.channelSetting.validate().then(() => {
          const channelData = this.getChannelSetting()
          channelData.channelInfo.chName = this.encodeChannelName(channelData.channelInfo.chName)
          const options = merge(this.defQueryOption, {
            paraBin: {
              operation: 13,
            },
            decodeMsgType: 'RepeaterCurChInfo',
          })

          repeaterWfMod
            .writeInData(channelData, options)
            .then(res => {
              return this.saveChannelData(channelData, true)
            })
            .catch(err => {
              bfglob.console.error('writeInCurChInfo', err)
            })
        })
      },
    },
    watch: {
      'channelSetting.chId'(val) {
        // 找到新的w信道id配置，重置表单各项
        const allChannelSetting = this.getAllChannelSetting()

        // 变更信道时，移除表单检验结果
        if (this.$refs.channelSetting && typeof this.$refs.channelSetting.clearValidate === 'function') {
          this.$refs.channelSetting.clearValidate()
        }

        const defaultData = {
          chId: val,
          chName: '',
          chType: 0,
          colourCodes: 1,
          rxFrequency: 0,
          txFrequency: 0,
          txPower: 0,
        }
        const newChannelSetting = allChannelSetting && allChannelSetting[val] ? allChannelSetting[val] : defaultData

        this.setChannelSetting(newChannelSetting)
      },
      'repeaterCurChInfo.channelInfo': {
        deep: true,
        handler(val) {
          this.channelSetting = merge(this.channelSetting, val)
        },
      },
      'repeaterData.writeFrequencySetting.allChannelSetting': {
        immediate: true,
        deep: true,
        handler(val) {
          if (this.queryCurChInfo) {
            return
          }

          const data = (val && val[this.channelSetting.chId]) || ChannelSetting
          this.channelSetting = merge({}, data)
        },
      },
      'repeaterData.writeFrequencySetting.repeaterCurChInfo': {
        immediate: true,
        deep: true,
        handler(val) {
          this.repeaterCurChInfo = merge({}, val || RepeaterCurChInfo)
        },
      },
    },
    computed: {
      labelWidth() {
        return convertPxToRem(calcScaleSize(150)) + 'rem'
      },
      defQueryOption() {
        return {
          sid: this.repeater,
          paraInt: this.getRepeaterId(),
          paraBin: {
            operation: 5,
          },
          decodeMsgType: 'RepeaterOneChannel',
          packageName: this.packageName,
        }
      },
      channelTypes() {
        // 0-数字信道
        return [
          {
            label: this.$t('dialog.digitalChannel'),
            value: 0,
          },
        ]
      },
      txPowerTypes() {
        // BF8100: 0=低功率 1=高功率
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.high'),
            value: 1,
          },
        ]
      },
      repeaterInfo() {
        return (this.repeaterData && this.repeaterData.writeFrequencySetting && this.repeaterData.writeFrequencySetting.repeaterInfo) || {}
      },
      channelSettingRules() {
        const frequencyValidator = (rule, value, callback) => {
          if (!value) {
            callback(new Error(this.$t('dialog.requiredRule')))
          } else if (isNaN(value)) {
            callback(new Error(this.$t('msgbox.mustNumber')))
          } else if (!this.repeaterInfo.lowFrequency || !this.repeaterInfo.highFrequency) {
            // 没有获取到高、低频信息，不做规则验证
            callback()
          } else if (parseFloat(value) < parseFloat(this.repeaterInfo.lowFrequency)) {
            callback(new Error(this.$t('msgbox.lowFrequency')))
          } else if (parseFloat(value) > parseFloat(this.repeaterInfo.highFrequency)) {
            callback(new Error(this.$t('msgbox.highFrequency')))
          } else {
            callback()
          }
        }
        return {
          txFrequency: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                frequencyValidator(rule, value, callback)
              },
              trigger: ['blur'],
            },
          ],
          rxFrequency: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                frequencyValidator(rule, value, callback)
              },
              trigger: ['blur'],
            },
          ],
          chName: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                // 检测输入的字符串是否有中文
                bfutil.cannotIncludeChineseRule(rule, value, callback)
              },
              trigger: ['blur'],
            },
          ],
        }
      },
    },
    components: {
      FrequencyMhz: defineAsyncComponent(() => import('@/components/common/FrequencyMhz')),
      bfInput,
      bfInputNumberV2,
      bfSelect,
      bfButton,
      EllipsisText,
    },
  }
</script>

<style lang="scss">
  .form-label {
    height: 50px;
    line-height: 50px;
  }

  .el-form.tr805005-channel {
    .el-form-item.actions .el-form-item__content {
      justify-content: center;
      align-items: center;
      gap: 12px;

      .el-button + .el-button {
        margin-left: unset;
      }
    }
  }
</style>
